#!/usr/bin/env python3
"""
Migration script to convert credentials table to variables table
with support for both credentials and global variables.

This script:
1. Creates the new variables table with type column
2. Migrates existing credentials data
3. Adds appropriate indexes
4. Provides rollback functionality
"""

import sys
import os
import logging
from datetime import datetime
from sqlalchemy import create_engine, text, MetaData, Table, Column, String, DateTime, Enum, ForeignKey
from sqlalchemy.orm import sessionmaker
from sqlalchemy.exc import SQLAlchemyError

# Add the parent directory to the path to import app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.config import settings
from app.models.credential import VariableType

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CredentialToVariableMigration:
    def __init__(self):
        self.engine = create_engine(settings.DATABASE_URL)
        self.Session = sessionmaker(bind=self.engine)
        self.metadata = MetaData()
        
    def check_prerequisites(self):
        """Check if migration can be run safely"""
        logger.info("Checking migration prerequisites...")
        
        with self.engine.connect() as conn:
            # Check if credentials table exists
            result = conn.execute(text("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = 'credentials'
                );
            """))
            credentials_exists = result.scalar()
            
            # Check if variables table already exists
            result = conn.execute(text("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = 'variables'
                );
            """))
            variables_exists = result.scalar()
            
            if not credentials_exists:
                raise Exception("Credentials table does not exist. Nothing to migrate.")
                
            if variables_exists:
                logger.warning("Variables table already exists. Migration may have been run before.")
                return False
                
            # Check for any foreign key constraints that might cause issues
            result = conn.execute(text("""
                SELECT COUNT(*) FROM credentials;
            """))
            credential_count = result.scalar()
            
            logger.info(f"Found {credential_count} credentials to migrate")
            return True
    
    def create_variables_table(self):
        """Create the new variables table"""
        logger.info("Creating variables table...")
        
        with self.engine.connect() as conn:
            # Create the variables table
            conn.execute(text("""
                CREATE TABLE variables (
                    id VARCHAR PRIMARY KEY,
                    key_name VARCHAR NOT NULL,
                    description VARCHAR,
                    value VARCHAR NOT NULL,
                    type VARCHAR(20) NOT NULL DEFAULT 'credential',
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    owner_id VARCHAR NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (owner_id) REFERENCES users(id)
                );
            """))
            
            # Create indexes for better performance
            conn.execute(text("""
                CREATE INDEX idx_variables_owner_id ON variables(owner_id);
            """))
            
            conn.execute(text("""
                CREATE INDEX idx_variables_owner_type ON variables(owner_id, type);
            """))
            
            conn.execute(text("""
                CREATE UNIQUE INDEX idx_variables_owner_key_type ON variables(owner_id, key_name, type);
            """))
            
            conn.commit()
            logger.info("Variables table created successfully")
    
    def migrate_data(self):
        """Migrate data from credentials to variables table"""
        logger.info("Migrating credential data to variables table...")
        
        with self.engine.connect() as conn:
            # Insert all existing credentials as 'credential' type variables
            result = conn.execute(text("""
                INSERT INTO variables (
                    id, key_name, description, value, type, 
                    updated_at, owner_id, created_at, last_used_at
                )
                SELECT 
                    id, key_name, description, value, 'credential',
                    updated_at, owner_id, created_at, last_used_at
                FROM credentials;
            """))
            
            migrated_count = result.rowcount
            conn.commit()
            
            logger.info(f"Successfully migrated {migrated_count} credentials to variables table")
            return migrated_count
    
    def verify_migration(self):
        """Verify that migration was successful"""
        logger.info("Verifying migration...")
        
        with self.engine.connect() as conn:
            # Count records in both tables
            credentials_count = conn.execute(text("SELECT COUNT(*) FROM credentials")).scalar()
            variables_count = conn.execute(text("SELECT COUNT(*) FROM variables WHERE type = 'credential'")).scalar()
            
            if credentials_count != variables_count:
                raise Exception(f"Migration verification failed: {credentials_count} credentials vs {variables_count} variables")
            
            # Verify data integrity by checking a few sample records
            sample_check = conn.execute(text("""
                SELECT c.id, c.key_name, c.value, v.id, v.key_name, v.value
                FROM credentials c
                JOIN variables v ON c.id = v.id
                WHERE v.type = 'credential'
                LIMIT 5;
            """)).fetchall()
            
            for row in sample_check:
                if row[0] != row[3] or row[1] != row[4] or row[2] != row[5]:
                    raise Exception(f"Data integrity check failed for record {row[0]}")
            
            logger.info("Migration verification successful")
    
    def create_backup(self):
        """Create a backup of the credentials table"""
        logger.info("Creating backup of credentials table...")
        
        backup_table_name = f"credentials_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        with self.engine.connect() as conn:
            conn.execute(text(f"""
                CREATE TABLE {backup_table_name} AS 
                SELECT * FROM credentials;
            """))
            conn.commit()
            
            logger.info(f"Backup created as table: {backup_table_name}")
            return backup_table_name
    
    def run_migration(self, create_backup=True):
        """Run the complete migration process"""
        try:
            logger.info("Starting credentials to variables migration...")
            
            # Check prerequisites
            if not self.check_prerequisites():
                logger.info("Migration prerequisites not met or already completed")
                return False
            
            # Create backup if requested
            backup_table = None
            if create_backup:
                backup_table = self.create_backup()
            
            # Create new variables table
            self.create_variables_table()
            
            # Migrate data
            migrated_count = self.migrate_data()
            
            # Verify migration
            self.verify_migration()
            
            logger.info(f"Migration completed successfully! Migrated {migrated_count} records.")
            if backup_table:
                logger.info(f"Backup table created: {backup_table}")
            
            logger.info("IMPORTANT: Update your application code to use the new Variable model before dropping the credentials table")
            
            return True
            
        except Exception as e:
            logger.error(f"Migration failed: {str(e)}")
            logger.error("Rolling back changes...")
            self.rollback()
            raise
    
    def rollback(self):
        """Rollback migration by dropping variables table"""
        try:
            logger.info("Rolling back migration...")
            with self.engine.connect() as conn:
                conn.execute(text("DROP TABLE IF EXISTS variables CASCADE;"))
                conn.commit()
            logger.info("Rollback completed")
        except Exception as e:
            logger.error(f"Rollback failed: {str(e)}")
    
    def cleanup_old_table(self):
        """Drop the old credentials table (run this after confirming everything works)"""
        logger.warning("This will permanently delete the credentials table!")
        confirmation = input("Are you sure you want to drop the credentials table? (yes/no): ")
        
        if confirmation.lower() == 'yes':
            try:
                with self.engine.connect() as conn:
                    conn.execute(text("DROP TABLE credentials CASCADE;"))
                    conn.commit()
                logger.info("Credentials table dropped successfully")
            except Exception as e:
                logger.error(f"Failed to drop credentials table: {str(e)}")
        else:
            logger.info("Cleanup cancelled")

def main():
    """Main function to run migration"""
    migration = CredentialToVariableMigration()
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "migrate":
            migration.run_migration()
        elif command == "rollback":
            migration.rollback()
        elif command == "cleanup":
            migration.cleanup_old_table()
        elif command == "verify":
            migration.verify_migration()
        else:
            print("Usage: python migrate_credentials_to_variables.py [migrate|rollback|cleanup|verify]")
    else:
        # Default: run migration
        migration.run_migration()

if __name__ == "__main__":
    main()
