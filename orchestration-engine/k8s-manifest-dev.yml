apiVersion: v1
kind: ServiceAccount
metadata:
  name: orchestration-engine-service-ai-sa
  namespace: ruh-catalyst
  labels:
    name: orchestration-engine-service-ai-sa
    namespace: ruh-catalyst
    app: orchestration-engine-service-ai
    deployment: orchestration-engine-service-ai-dp
---
# Create Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: orchestration-engine-service-ai-dp
  namespace: ruh-catalyst
  labels:
    name: orchestration-engine-service-ai-dp
    namespace: ruh-catalyst
    app: orchestration-engine-service-ai
    serviceaccount: orchestration-engine-service-ai-sa
    deployment: orchestration-engine-service-ai-dp
spec:
  replicas: 1
  selector:
    matchLabels:
      app: orchestration-engine-service-ai
      deployment: orchestration-engine-service-ai-dp
  template:
    metadata:
      labels:
        namespace: ruh-catalyst
        app: orchestration-engine-service-ai
        deployment: orchestration-engine-service-ai-dp
    spec:
      serviceAccountName: orchestration-engine-service-ai-sa      
      containers:
      - name: orchestration-engine-service-ai
        image: us-central1-docker.pkg.dev/<PROJECT_ID>/<REPOSITORY>/<IMAGE_NAME>:<ENV>-<VERSION>
        resources:
          requests:
            memory: 64Mi
            cpu: 50m
          limits:
            memory: 1024Mi
            cpu: 250m
        ports:
        - containerPort: 50052
      #   readinessProbe:
      #     tcpSocket:
      #       port: 5001
      #     initialDelaySeconds: 5
      #     periodSeconds: 10
      #   livenessProbe:
      #     tcpSocket:
      #       port: 5001
      #     initialDelaySeconds: 15
      #     periodSeconds: 20
      # tolerations:
      # - key: "spotInstance"
      #   operator: "Equal"
      #   value: "true"
      #   effect: "PreferNoSchedule"
      # nodeSelector:    
      #   eks.amazonaws.com/capacityType: SPOT       
---
#### Create Service
apiVersion: v1
kind: Service
metadata:
  name: orchestration-engine-service-ai-svc
  namespace: ruh-catalyst
spec:
  selector:
    app: orchestration-engine-service-ai
    deployment: orchestration-engine-service-ai-dp
  ports:
    - protocol: TCP
      port: 80
      targetPort: 50052
  sessionAffinityConfig:
    clientIP:
      timeoutSeconds: 100000
---
### Create HPA
# apiVersion: autoscaling/v2beta1
# kind: HorizontalPodAutoscaler
# metadata:
#   name:orchestration-engine-service-orchestration-engine-hpa
#   namespace:ruh-catalyst
# spec:
#   scaleTargetRef:
#     apiVersion: apps/v1
#     kind: Deployment
#     name:orchestration-engine-service-orchestration-engine-dp
#   minReplicas: 1
#   maxReplicas: 2
#   metrics:
#     - type: Resource
#       resource:
#         name: cpu
#         targetAverageUtilization: 60
---
### Create Nginx Ingress
# apiVersion: networking.k8s.io/v1
# kind: Ingress
# metadata:
#   name: orchestration-engine-service-orchestration-engine-ingress
#   namespace: ruh-catalyst
# spec:
#   ingressClassName: nginx
#   rules:
#   - host: orchestration-engine-service-dev.rapidinnovation.dev
#     http:
#       paths:
#       - path: /
#         pathType: Prefix
#         backend:
#           service:
#             name: orchestration-engine-service-ai-svc
#             port:
#               number: 80